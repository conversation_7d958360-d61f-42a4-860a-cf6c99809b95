@using MediatR
@using PrinterService
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WingCore.domain.Models.StammDbModels
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using WingCore.application.PrintableDtos
@using WingCore.domain.Common.Configurations
@using WinGng.RazorLib.Components.Pages.PrintView
@using wingPrinterListLabel.application.Contracts
@using wingPrinterListLabel.application.Invoices
@using wingPrinterListLabel.application.RepositoryItems
@using wingPrinterListLabel.domain

@inherits BasePage


@inject IConfigurationService ConfigurationService
@inject DialogService DialogService
@inject ICountryService CountryService
@inject IApplicationPath ApplicationPath
@inject ISender Sender

@inject IListLabelFactory ListLabelFactory

<LoadingIndicator @ref="_loadingIndicatorLoadConfigData"
                  DoLoadDataCallback="LoadConfigData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>
<LoadingIndicator @ref="_loadingIndicatorToEmailView"
                  DoLoadDataCallbackWithArg="async (arg) => await OpenEmailView((string)arg)"
                  Option="_optionsIndicatorToEmailView" DialogService="DialogService"/>

<RadzenContent Container="main"  Style="min-width: 100%">
    <ChildContent>
        <RadzenRow >
            <RadzenHeading Size="H1" style="display: inline-block" Text=@Localizer["ElectronicWgsConfiguration"]/>
        </RadzenRow>
        <RadzenRow Gap="1rem" style="font-size: large">
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text=@Localizer["SaveConfiguration"] Click="@SaveConfigData"/>
            </RadzenStack>
        </RadzenRow>
        <RadzenTabs RenderMode="TabRenderMode.Client" TabPosition="@TabPosition.Top" >
            <Tabs>
                <RadzenTabsItem Text=@Localizer["Configuration"]>
                    <ElectricWgsConfigurationEmailComponent
                        DialogService = "@DialogService"
                        ConfigurationElectricWgs="@ConfigurationElectricWgs"
                        Localizer="@Localizer"/>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@Localizer["Countries"]>
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                            <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="add" Text=@Localizer["AddCountry"] Click="@AddCountryButton"/>
                        </RadzenStack>
                    </RadzenRow>
                    <RadzenTabs @ref="TabsCountry" RenderMode="TabRenderMode.Client" TabPosition="@TabPosition.Top" @bind-SelectedIndex="SelectedLanguageTab">
                        <Tabs>
                            @foreach (var (key, value) in ConfigurationElectricWgs.LanguageToEmailFields)
                            {
                                <RadzenTabsItem Text="@key">
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text=@Localizer["LanguageCode"]/>
                                                <RadzenTextBox @bind-Value="@value.LanguageCode" Style="width:95%"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text=@Localizer["Subject"]/>
                                                <RadzenTextBox @bind-Value="@value.Subject" Style="width:95%"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text=@Localizer["FileName"]/>
                                                <RadzenTextBox @bind-Value="@value.FileName" Style="width:95%"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text=@Localizer["LayoutForPrint"]/>
                                                <RadzenDropDown @bind-Value="@value.LayoutForPrint" Data="@_allLayoutCanUseKeyToDescription" TextProperty="Description" ValueProperty="Key" Style="width:95%"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenColumn Size="12">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text=@Localizer["EmailBodyHtml"]/>
                                                <RadzenHtmlEditor @bind-Value="@value.EmailBodyHtml" Style="@HtmlHeight"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem" style="font-size: large">
                                        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                                            <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="email" Text=@Localizer["TestEmail"] Click="@(async () => await TestEmailButton(key))"/>
                                            <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="delete" Text=@Localizer["DeleteCountry"] Click="@(async () => await DeleteCountryButton(key))"/>
                                        </RadzenStack>
                                    </RadzenRow>
                                </RadzenTabsItem>
                            }
                        </Tabs>
                    </RadzenTabs>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@Localizer["AvailableFields"]>
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel Text=@Localizer["AvailableFields"]/>
                                <RadzenListBox @bind-Value="_value" Data="@_fieldsName" Style="width: 100%; height: 400px;" />
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </ChildContent>
</RadzenContent>


@code {
    [Parameter] public ConfigurationElectricWgs ConfigurationElectricWgs { get; set; } = new();
    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(false, false, false, false, 0, 6);

    LoadingIndicator? _loadingIndicatorToEmailView;
    readonly LoadingIndicatorOptions _optionsIndicatorToEmailView = new(false, false, false, false);
    
    LoadingIndicator? _loadingIndicatorLoadConfigData;
    IEnumerable<string> _fieldsName = [];
    string _value = string.Empty;
    private string HtmlHeight => $"height: {(Dimensions.Height < 600 ? 500 : Dimensions.Height - 400)}px; width:100%";
    
    IEnumerable<LandKennung> _allCountries = [];
    string SelectedLand { get; set; } = string.Empty;
    
    RadzenTabs? TabsCountry;
    int SelectedLanguageTab { get; set; }
        
    IEnumerable<CustomizedRepositoryItem> _allLayoutCanUseKeyToDescription = [];

    private async Task ReloadLayoutItems()
    {
        try
        {
            _allLayoutCanUseKeyToDescription = await Sender.Send(new AllRepositoryItemsFromPrintObjectQuery(LabelWiegeschein.Dummy()));
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
    

    private async Task LoadConfigDataButton()
    {
        if (_loadingIndicatorLoadConfigData is null)
            return;

        await _loadingIndicatorLoadConfigData.Run();
    }
    
    public async Task LoadConfigData()
    {
        try
        {
            ConfigurationElectricWgs = await ConfigurationService.GetConfigurationElectricWgsAsync();
            _fieldsName = PrinterCreator.GetVariablesFromObject(LabelWiegeschein.Dummy()).Keys;
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    bool _saveConfigDataStart;
    
    private async Task SaveConfigData()
    {
        if (_saveConfigDataStart)
            return;

        _saveConfigDataStart = true;
        try
        {
            var id = await ConfigurationService.SetConfigurationElectricWgsAsync(ConfigurationElectricWgs);
            await LoadConfigData();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            _saveConfigDataStart = false;
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await LoadConfigData();
        await ReloadLayoutItems();
        await LoadCountries();
    }

    private async Task LoadCountries()
    {
        try
        {
            _allCountries = await CountryService.GetAllCountries();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
     
    void AddLanguageEmailFieldToConfig()
    {
        if (string.IsNullOrWhiteSpace(SelectedLand))
        {
            DialogService.Close();
            return;
        }
        
        if(!ConfigurationElectricWgs.LanguageToEmailFields.ContainsKey(SelectedLand))
            ConfigurationElectricWgs.LanguageToEmailFields.Add(SelectedLand, new LanguageAndEmailFields());
        
        SelectedLanguageTab = ConfigurationElectricWgs.LanguageToEmailFields.Count;
        
        TabsCountry?.Reload();
        StateHasChanged();
        DialogService.Close();
    }

    private async Task AddCountryButton()
    {
        await DialogService.OpenAsync<CountrySelectionDialog>(Localizer["SelectCountry"],
            new Dictionary<string, object>()
            {
                { "AllCountries", _allCountries },
                { "SelectedLand", SelectedLand },
                { "OnCountrySelected", new Action<string>(country => SelectedLand = country) },
                { "OnConfirm", new Action(AddLanguageEmailFieldToConfig) }
            },
            new DialogOptions()
            {
                Width = "30%",
                Height = "40%",
                ShowClose = true,
                Resizable = false,
                Draggable = true
            });
    }

    private async Task DeleteCountryButton(string countryKey)
    {
        var confirmed = await DialogService.Confirm(Localizer["AreYouSure"], Localizer["ReallyDeleteCountryConfiguration"], new ConfirmOptions() { OkButtonText = Localizer["Yes"], CancelButtonText = Localizer["No"] });
        if (confirmed != true) return;

        ConfigurationElectricWgs.LanguageToEmailFields.Remove(countryKey);
        TabsCountry?.Reload();
        StateHasChanged();
    }

    private async Task TestEmailButton(string key)
    {
        if (_loadingIndicatorToEmailView is null)
            return;

        await _loadingIndicatorToEmailView.Run(key);
    }
    
    private async Task OpenEmailView(string key)
    {
        try
        {
            
            var command = new DummyWgsSendEmailCommand("",key,ConfigurationElectricWgs);
            var emailDataToCreateEmail = await Sender.Send(command);

            if (emailDataToCreateEmail is null)
            {
                await DialogService.ShowError(Localizer["EmailCouldNotBeCreated"]);
                return;
            }
            
            await DialogService.OpenAsync<EmailViewPage>(Localizer["EmailTestPreview"],
                new Dictionary<string, object>()
                {
                    { "EmailDataToCreateEmailParam", emailDataToCreateEmail },
                    { "ShowHeaderText", false },
                    { "DialogService", DialogService }
                },
                new DialogOptions()
                {
                    Resizable = false,
                    Draggable = false,

                    Width = "90%",
                    Height = "100%"
                });
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
}
