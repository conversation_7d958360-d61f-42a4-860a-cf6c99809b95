@using WingCore.domain.Models.StammDbModels

<RadzenStack Orientation="Orientation.Vertical" Gap="1rem" class="rz-h-100 rz-p-4">
    <RadzenDropDown @bind-Value="@SelectedLand"
                    Data="@AllCountries"
                    TextProperty="@nameof(LandKennung.Landesbezeichnung)"
                    TValue="@string"
                    ValueProperty="IntraKennung"
                    AllowClear="true"
                    AllowFiltering="true" 
                    InputAttributes="@(new Dictionary<string,object>(){ { "aria-label", Localizer["SelectCountry"] }})"
                    Placeholder=@Localizer["SelectCountry"] />
    <RadzenButton Click="OnConfirmClick" Icon="add" Text=@Localizer["Add"]></RadzenButton>
</RadzenStack>

@code {
    [Parameter] public IEnumerable<LandKennung> AllCountries { get; set; } = [];
    [Parameter] public string SelectedLand { get; set; } = string.Empty;
    [Parameter] public Action<string>? OnCountrySelected { get; set; }
    [Parameter] public Action? OnConfirm { get; set; }
    //[Parameter] public IStringLocalizer Localizer { get; set; } = null!;

    private void OnConfirmClick()
    {
        OnCountrySelected?.Invoke(SelectedLand);
        OnConfirm?.Invoke();
    }
}
