@using Licencing
@using QuartzScheduler
@using WingCore.application.Contract.Services
@using WingCore.domain.Common.Configurations
@using WinGng.RazorLib.Components.Pages.Scheduler

@inject SeedData SeedData

<RadzenRow Gap="1rem" style="font-size: large">
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenLabel Text=@Localizer["SmtpServerAddress"]/>
            <RadzenTextBox @bind-Value="@ConfigurationElectricWgs.SmtpServerAddress" Style="width:95%"/>
        </RadzenStack>
    </RadzenColumn>
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenLabel Text=@Localizer["SmtpServerPort"]/>
            <RadzenNumeric @bind-Value="@ConfigurationElectricWgs.SmtpServerPort" Style="width:95%"/>
        </RadzenStack>
    </RadzenColumn>
</RadzenRow>
<RadzenRow Gap="1rem" style="font-size: large">
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenLabel Text=@Localizer["SmtpServerUser"]/>
            <RadzenTextBox @bind-Value="@ConfigurationElectricWgs.SmtpServerUser" Style="width:95%"/>
        </RadzenStack>
    </RadzenColumn>
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenLabel Text=@Localizer["SmtpServerPassword"]/>
            <RadzenPassword @bind-Value="@ConfigurationElectricWgs.SmtpServerPassword" Style="width:95%"/>
        </RadzenStack>
    </RadzenColumn>
</RadzenRow>
<RadzenRow Gap="1rem" style="font-size: large">
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenLabel Text=@Localizer["SenderEmail"]/>
            <RadzenTextBox @bind-Value="@ConfigurationElectricWgs.SmtpSenderAddress" Style="width:95%"/>
        </RadzenStack>
    </RadzenColumn>
</RadzenRow>
<RadzenRow Gap="1rem" style="font-size: large">
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <CascadingAuthenticationState>
                <AuthorizeView Policy="@LicenseClaimType.SchedulerValue">
                    <RadzenButton Icon="calendar_clock" Click="CreateScheduler" ButtonStyle="ButtonStyle.Primary" Text=@Localizer["CreateJob"]/>
                </AuthorizeView>
            </CascadingAuthenticationState>
        </RadzenStack>
    </RadzenColumn>
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenLabel Text=@Localizer["SenderName"]/>
            <RadzenTextBox @bind-Value="@ConfigurationElectricWgs.SmtpSenderName" Style="width:95%"/>
        </RadzenStack>
    </RadzenColumn>
</RadzenRow>
<RadzenRow Gap="1rem" style="font-size: large">
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <RadzenLabel Text=@Localizer["SmtpSocketTimeout"]/>
            <RadzenNumeric @bind-Value="@ConfigurationElectricWgs.SmtpSocketTimeout" Style="width:95%"/>
        </RadzenStack>
    </RadzenColumn>
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <div>
                <RadzenCheckBox @bind-Value="@ConfigurationElectricWgs.SmtpSecureConnection" Name="CheckSmtpSecureConnection" Style="margin-left: 8px"/>
                <RadzenLabel Text="@Localizer["SmtpSecureConnection"]" Component="CheckSmtpSecureConnection" Style="margin-left: 8px; vertical-align: middle;"/>
            </div>
        </RadzenStack>
    </RadzenColumn>
</RadzenRow>
<RadzenRow Gap="1rem" style="font-size: large">
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <div>
                <RadzenCheckBox @bind-Value="@ConfigurationElectricWgs.ShowDialog" Name="CheckShowDialog" Style="margin-left: 8px"/>
                <RadzenLabel Text="@Localizer["ShowDialog"]" Component="CheckShowDialog" Style="margin-left: 8px; vertical-align: middle;"/>
            </div>
        </RadzenStack>
    </RadzenColumn>
    <RadzenColumn Size="12" SizeSM="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
            <div>
                <RadzenCheckBox @bind-Value="@ConfigurationElectricWgs.ForceSendMailInSeparateThread" Name="CheckForceSendMailInSeparateThread" Style="margin-left: 8px"/>
                <RadzenLabel Text="@Localizer["ForceSendMailInSeparateThread"]" Component="CheckForceSendMailInSeparateThread" Style="margin-left: 8px; vertical-align: middle;"/>
            </div>
        </RadzenStack>
    </RadzenColumn>
</RadzenRow>

@code {
    [Parameter] public DialogService DialogService { get; set; } = null!;
    [Parameter] public ConfigurationElectricWgs ConfigurationElectricWgs { get; set; } = new();
    
    async Task CreateScheduler()
    {
        try
        {
            var jobViewModel = new JobViewModel() with
            {
                Gruppe = "EWGS",
                SelectedJobClass = SeedData.JobsNameSendEWgsPerMailJob
            };

            JobViewModel? data = await DialogService.OpenAsync<CreateJob>(Localizer["CreateJob"],
                new Dictionary<string, object>
                {
                    { "JobViewModel", jobViewModel }
                },
                new DialogOptions()
                {
                    Width = "30%",
                    Height = "40%",
                    ShowClose = true,
                    Resizable = false,
                    Draggable = true
                });

            if (data?.TimeIntervallInSec is null)
                return;
            
            var result = await SeedData.CheckAndCreateJobsData(
                data.Name,
                data.Gruppe,
                data.ServerName,
                data.TimeIntervallInSec.Value,
                data.SelectedJobClass);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
}
