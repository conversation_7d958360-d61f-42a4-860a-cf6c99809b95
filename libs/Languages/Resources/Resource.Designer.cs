//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Languages.Resources {
    using System;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource {
        
        private static System.Resources.ResourceManager resourceMan;
        
        private static System.Globalization.CultureInfo resourceCulture;
        
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Resources.ResourceManager ResourceManager {
            get {
                if (object.Equals(null, resourceMan)) {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("Languages.Resources.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        public static string Connect {
            get {
                return ResourceManager.GetString("Connect", resourceCulture);
            }
        }
        
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        public static string FromDate {
            get {
                return ResourceManager.GetString("FromDate", resourceCulture);
            }
        }
        
        public static string ToDate {
            get {
                return ResourceManager.GetString("ToDate", resourceCulture);
            }
        }
        
        public static string FromInvoiceNumber {
            get {
                return ResourceManager.GetString("FromInvoiceNumber", resourceCulture);
            }
        }
        
        public static string ToInvoiceNumber {
            get {
                return ResourceManager.GetString("ToInvoiceNumber", resourceCulture);
            }
        }
        
        public static string OutgoingInvoice {
            get {
                return ResourceManager.GetString("OutgoingInvoice", resourceCulture);
            }
        }
        
        public static string IncomingInvoice {
            get {
                return ResourceManager.GetString("IncomingInvoice", resourceCulture);
            }
        }
        
        public static string EInvoices {
            get {
                return ResourceManager.GetString("EInvoices", resourceCulture);
            }
        }
        
        public static string GrainAccounting {
            get {
                return ResourceManager.GetString("GrainAccounting", resourceCulture);
            }
        }
        
        public static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        public static string DeactivatePaging {
            get {
                return ResourceManager.GetString("DeactivatePaging", resourceCulture);
            }
        }
        
        public static string ActivatePaging {
            get {
                return ResourceManager.GetString("ActivatePaging", resourceCulture);
            }
        }
        
        public static string PagingSummaryFormat {
            get {
                return ResourceManager.GetString("PagingSummaryFormat", resourceCulture);
            }
        }
        
        public static string GroupPanelText {
            get {
                return ResourceManager.GetString("GroupPanelText", resourceCulture);
            }
        }
        
        public static string SelectAllItems {
            get {
                return ResourceManager.GetString("SelectAllItems", resourceCulture);
            }
        }
        
        public static string SelectItem {
            get {
                return ResourceManager.GetString("SelectItem", resourceCulture);
            }
        }
        
        public static string InvoiceNumber {
            get {
                return ResourceManager.GetString("InvoiceNumber", resourceCulture);
            }
        }
        
        public static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        public static string Type {
            get {
                return ResourceManager.GetString("Type", resourceCulture);
            }
        }
        
        public static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        public static string CreationDate {
            get {
                return ResourceManager.GetString("CreationDate", resourceCulture);
            }
        }
        
        public static string TotalAmount {
            get {
                return ResourceManager.GetString("TotalAmount", resourceCulture);
            }
        }
        
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        public static string EInvoice {
            get {
                return ResourceManager.GetString("EInvoice", resourceCulture);
            }
        }
        
        public static string Exported {
            get {
                return ResourceManager.GetString("Exported", resourceCulture);
            }
        }
        
        public static string EmailSent {
            get {
                return ResourceManager.GetString("EmailSent", resourceCulture);
            }
        }
        
        public static string Invoices {
            get {
                return ResourceManager.GetString("Invoices", resourceCulture);
            }
        }
        
        public static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        public static string Export {
            get {
                return ResourceManager.GetString("Export", resourceCulture);
            }
        }
        
        public static string Taxes {
            get {
                return ResourceManager.GetString("Taxes", resourceCulture);
            }
        }
        
        public static string Positions {
            get {
                return ResourceManager.GetString("Positions", resourceCulture);
            }
        }
        
        public static string Invoicing {
            get {
                return ResourceManager.GetString("Invoicing", resourceCulture);
            }
        }
        
        public static string InvoiceOverview {
            get {
                return ResourceManager.GetString("InvoiceOverview", resourceCulture);
            }
        }
        
        public static string CancellationNumber {
            get {
                return ResourceManager.GetString("CancellationNumber", resourceCulture);
            }
        }
        
        public static string EmailForEInvoice {
            get {
                return ResourceManager.GetString("EmailForEInvoice", resourceCulture);
            }
        }
        
        public static string EmailPreview {
            get {
                return ResourceManager.GetString("EmailPreview", resourceCulture);
            }
        }
        
        public static string SearchParameters {
            get {
                return ResourceManager.GetString("SearchParameters", resourceCulture);
            }
        }
        
        public static string PDFViewer {
            get {
                return ResourceManager.GetString("PDFViewer", resourceCulture);
            }
        }
        
        public static string OwnStepText {
            get {
                return ResourceManager.GetString("OwnStepText", resourceCulture);
            }
        }
        
        public static string OutgoingInvoiceInformation {
            get {
                return ResourceManager.GetString("OutgoingInvoiceInformation", resourceCulture);
            }
        }
        
        public static string OpenDesigner {
            get {
                return ResourceManager.GetString("OpenDesigner", resourceCulture);
            }
        }
        
        public static string OpenInvoice {
            get {
                return ResourceManager.GetString("OpenInvoice", resourceCulture);
            }
        }
        
        public static string NoInvoicesFound {
            get {
                return ResourceManager.GetString("NoInvoicesFound", resourceCulture);
            }
        }
        
        public static string ErrorWhenOpeningThePDF {
            get {
                return ResourceManager.GetString("ErrorWhenOpeningThePDF", resourceCulture);
            }
        }
        
        public static string CancellationInvoiceNumber {
            get {
                return ResourceManager.GetString("CancellationInvoiceNumber", resourceCulture);
            }
        }
        
        public static string InvoiceHolder {
            get {
                return ResourceManager.GetString("InvoiceHolder", resourceCulture);
            }
        }
        
        public static string InvoiceDate {
            get {
                return ResourceManager.GetString("InvoiceDate", resourceCulture);
            }
        }
        
        public static string InvoiceIssuer {
            get {
                return ResourceManager.GetString("InvoiceIssuer", resourceCulture);
            }
        }
        
        public static string OriginalInvoiceNumber {
            get {
                return ResourceManager.GetString("OriginalInvoiceNumber", resourceCulture);
            }
        }
        
        public static string TotalGross {
            get {
                return ResourceManager.GetString("TotalGross", resourceCulture);
            }
        }
        
        public static string TotalNet {
            get {
                return ResourceManager.GetString("TotalNet", resourceCulture);
            }
        }
        
        public static string TotalTaxes {
            get {
                return ResourceManager.GetString("TotalTaxes", resourceCulture);
            }
        }
        
        public static string ValueDate {
            get {
                return ResourceManager.GetString("ValueDate", resourceCulture);
            }
        }
        
        public static string AutoLogin {
            get {
                return ResourceManager.GetString("AutoLogin", resourceCulture);
            }
        }
        
        public static string ClientDatabase {
            get {
                return ResourceManager.GetString("ClientDatabase", resourceCulture);
            }
        }
        
        public static string ClientNo {
            get {
                return ResourceManager.GetString("ClientNo", resourceCulture);
            }
        }
        
        public static string DatabaseName {
            get {
                return ResourceManager.GetString("DatabaseName", resourceCulture);
            }
        }
        
        public static string DatabasePassword {
            get {
                return ResourceManager.GetString("DatabasePassword", resourceCulture);
            }
        }
        
        public static string DatabasePath {
            get {
                return ResourceManager.GetString("DatabasePath", resourceCulture);
            }
        }
        
        public static string DatabaseUsername {
            get {
                return ResourceManager.GetString("DatabaseUsername", resourceCulture);
            }
        }
        
        public static string ManageConnection {
            get {
                return ResourceManager.GetString("ManageConnection", resourceCulture);
            }
        }
        
        public static string MasterDatabase {
            get {
                return ResourceManager.GetString("MasterDatabase", resourceCulture);
            }
        }
        
        public static string MasterDatabaseNotReachable {
            get {
                return ResourceManager.GetString("MasterDatabaseNotReachable", resourceCulture);
            }
        }
        
        public static string NoDatabaseExistsForClient {
            get {
                return ResourceManager.GetString("NoDatabaseExistsForClient", resourceCulture);
            }
        }
        
        public static string OpenConfigurationFolder {
            get {
                return ResourceManager.GetString("OpenConfigurationFolder", resourceCulture);
            }
        }
        
        public static string WarehouseDatabase {
            get {
                return ResourceManager.GetString("WarehouseDatabase", resourceCulture);
            }
        }
        
        public static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        public static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        public static string Percent {
            get {
                return ResourceManager.GetString("Percent", resourceCulture);
            }
        }
        
        public static string TaxAmount {
            get {
                return ResourceManager.GetString("TaxAmount", resourceCulture);
            }
        }
        
        public static string NetAmount {
            get {
                return ResourceManager.GetString("NetAmount", resourceCulture);
            }
        }
        
        public static string Position {
            get {
                return ResourceManager.GetString("Position", resourceCulture);
            }
        }
        
        public static string ItemNumber {
            get {
                return ResourceManager.GetString("ItemNumber", resourceCulture);
            }
        }
        
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        public static string TotalDiscount {
            get {
                return ResourceManager.GetString("TotalDiscount", resourceCulture);
            }
        }
        
        public static string TotalNetWithDiscount {
            get {
                return ResourceManager.GetString("TotalNetWithDiscount", resourceCulture);
            }
        }
        
        public static string Account {
            get {
                return ResourceManager.GetString("Account", resourceCulture);
            }
        }
        
        public static string DeliveryNoteNumber {
            get {
                return ResourceManager.GetString("DeliveryNoteNumber", resourceCulture);
            }
        }
        
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        public static string GrainAccountingInformation {
            get {
                return ResourceManager.GetString("GrainAccountingInformation", resourceCulture);
            }
        }
        
        public static string IncomingInvoiceInformation {
            get {
                return ResourceManager.GetString("IncomingInvoiceInformation", resourceCulture);
            }
        }
        
        public static string VAT {
            get {
                return ResourceManager.GetString("VAT", resourceCulture);
            }
        }
        
        public static string VATAmount {
            get {
                return ResourceManager.GetString("VATAmount", resourceCulture);
            }
        }
        
        public static string TotalVAT {
            get {
                return ResourceManager.GetString("TotalVAT", resourceCulture);
            }
        }
        
        public static string Ausgangsrechnung {
            get {
                return ResourceManager.GetString("Ausgangsrechnung", resourceCulture);
            }
        }
        
        public static string Eingangsrechnung {
            get {
                return ResourceManager.GetString("Eingangsrechnung", resourceCulture);
            }
        }
        
        public static string Getreideabrechnung {
            get {
                return ResourceManager.GetString("Getreideabrechnung", resourceCulture);
            }
        }
        
        public static string MasterData {
            get {
                return ResourceManager.GetString("MasterData", resourceCulture);
            }
        }
        
        public static string CustomerData {
            get {
                return ResourceManager.GetString("CustomerData", resourceCulture);
            }
        }
        
        public static string CompanyMasterData {
            get {
                return ResourceManager.GetString("CompanyMasterData", resourceCulture);
            }
        }
        
        public static string ProcessArticles {
            get {
                return ResourceManager.GetString("ProcessArticles", resourceCulture);
            }
        }
        
        public static string BasicData {
            get {
                return ResourceManager.GetString("BasicData", resourceCulture);
            }
        }
        
        public static string CustomerNo {
            get {
                return ResourceManager.GetString("CustomerNo", resourceCulture);
            }
        }
        
        public static string Matchcode {
            get {
                return ResourceManager.GetString("Matchcode", resourceCulture);
            }
        }
        
        public static string Salutation {
            get {
                return ResourceManager.GetString("Salutation", resourceCulture);
            }
        }
        
        public static string ContactPerson {
            get {
                return ResourceManager.GetString("ContactPerson", resourceCulture);
            }
        }
        
        public static string Street {
            get {
                return ResourceManager.GetString("Street", resourceCulture);
            }
        }
        
        public static string ZipCodeCity {
            get {
                return ResourceManager.GetString("ZipCodeCity", resourceCulture);
            }
        }
        
        public static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        public static string FederalState {
            get {
                return ResourceManager.GetString("FederalState", resourceCulture);
            }
        }
        
        public static string Communication {
            get {
                return ResourceManager.GetString("Communication", resourceCulture);
            }
        }
        
        public static string Phone {
            get {
                return ResourceManager.GetString("Phone", resourceCulture);
            }
        }
        
        public static string Fax {
            get {
                return ResourceManager.GetString("Fax", resourceCulture);
            }
        }
        
        public static string Internet {
            get {
                return ResourceManager.GetString("Internet", resourceCulture);
            }
        }
        
        public static string IdentNumber {
            get {
                return ResourceManager.GetString("IdentNumber", resourceCulture);
            }
        }
        
        public static string ILN {
            get {
                return ResourceManager.GetString("ILN", resourceCulture);
            }
        }
        
        public static string Kdleh {
            get {
                return ResourceManager.GetString("Kdleh", resourceCulture);
            }
        }
        
        public static string CustomerSearch {
            get {
                return ResourceManager.GetString("CustomerSearch", resourceCulture);
            }
        }
        
        public static string SearchFor {
            get {
                return ResourceManager.GetString("SearchFor", resourceCulture);
            }
        }
        
        public static string NoCustomersFound {
            get {
                return ResourceManager.GetString("NoCustomersFound", resourceCulture);
            }
        }
        
        public static string Number {
            get {
                return ResourceManager.GetString("Number", resourceCulture);
            }
        }
        
        public static string ZipCode {
            get {
                return ResourceManager.GetString("ZipCode", resourceCulture);
            }
        }
        
        public static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        public static string POBox {
            get {
                return ResourceManager.GetString("POBox", resourceCulture);
            }
        }
        
        public static string Identifier {
            get {
                return ResourceManager.GetString("Identifier", resourceCulture);
            }
        }
        
        public static string Customers {
            get {
                return ResourceManager.GetString("Customers", resourceCulture);
            }
        }
        
        public static string New {
            get {
                return ResourceManager.GetString("New", resourceCulture);
            }
        }
        
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        public static string BasicArticle {
            get {
                return ResourceManager.GetString("BasicArticle", resourceCulture);
            }
        }
        
        public static string SelectAMainArticle {
            get {
                return ResourceManager.GetString("SelectAMainArticle", resourceCulture);
            }
        }
        
        public static string ReferenceNumber {
            get {
                return ResourceManager.GetString("ReferenceNumber", resourceCulture);
            }
        }
        
        public static string Bag {
            get {
                return ResourceManager.GetString("Bag", resourceCulture);
            }
        }
        
        public static string EanBag {
            get {
                return ResourceManager.GetString("EanBag", resourceCulture);
            }
        }
        
        public static string EanBagIsMandatory {
            get {
                return ResourceManager.GetString("EanBagIsMandatory", resourceCulture);
            }
        }
        
        public static string LabelType {
            get {
                return ResourceManager.GetString("LabelType", resourceCulture);
            }
        }
        
        public static string LabelTypeIsMandatory {
            get {
                return ResourceManager.GetString("LabelTypeIsMandatory", resourceCulture);
            }
        }
        
        public static string BagType {
            get {
                return ResourceManager.GetString("BagType", resourceCulture);
            }
        }
        
        public static string BagCount {
            get {
                return ResourceManager.GetString("BagCount", resourceCulture);
            }
        }
        
        public static string BagCountIsMandatory {
            get {
                return ResourceManager.GetString("BagCountIsMandatory", resourceCulture);
            }
        }
        
        public static string BagContent {
            get {
                return ResourceManager.GetString("BagContent", resourceCulture);
            }
        }
        
        public static string BagContentIsMandatory {
            get {
                return ResourceManager.GetString("BagContentIsMandatory", resourceCulture);
            }
        }
        
        public static string PrintLayoutForBagLabel {
            get {
                return ResourceManager.GetString("PrintLayoutForBagLabel", resourceCulture);
            }
        }
        
        public static string SelectPrintLayout {
            get {
                return ResourceManager.GetString("SelectPrintLayout", resourceCulture);
            }
        }
        
        public static string Dimensions {
            get {
                return ResourceManager.GetString("Dimensions", resourceCulture);
            }
        }
        
        public static string HeightCm {
            get {
                return ResourceManager.GetString("HeightCm", resourceCulture);
            }
        }
        
        public static string WidthCm {
            get {
                return ResourceManager.GetString("WidthCm", resourceCulture);
            }
        }
        
        public static string DepthCm {
            get {
                return ResourceManager.GetString("DepthCm", resourceCulture);
            }
        }
        
        public static string Note {
            get {
                return ResourceManager.GetString("Note", resourceCulture);
            }
        }
        
        public static string DescriptionIsMandatory {
            get {
                return ResourceManager.GetString("DescriptionIsMandatory", resourceCulture);
            }
        }
        
        public static string Pallet {
            get {
                return ResourceManager.GetString("Pallet", resourceCulture);
            }
        }
        
        public static string EanPallet {
            get {
                return ResourceManager.GetString("EanPallet", resourceCulture);
            }
        }
        
        public static string EanPalletIsMandatory {
            get {
                return ResourceManager.GetString("EanPalletIsMandatory", resourceCulture);
            }
        }
        
        public static string PalletType {
            get {
                return ResourceManager.GetString("PalletType", resourceCulture);
            }
        }
        
        public static string PalletizerRecipe {
            get {
                return ResourceManager.GetString("PalletizerRecipe", resourceCulture);
            }
        }
        
        public static string PrintCount {
            get {
                return ResourceManager.GetString("PrintCount", resourceCulture);
            }
        }
        
        public static string PrintCountIsMandatory {
            get {
                return ResourceManager.GetString("PrintCountIsMandatory", resourceCulture);
            }
        }
        
        public static string PrintLayoutForPalletLabel {
            get {
                return ResourceManager.GetString("PrintLayoutForPalletLabel", resourceCulture);
            }
        }
        
        public static string AdditionalData {
            get {
                return ResourceManager.GetString("AdditionalData", resourceCulture);
            }
        }
        
        public static string BestBeforeDateMonth {
            get {
                return ResourceManager.GetString("BestBeforeDateMonth", resourceCulture);
            }
        }
        
        public static string BestBeforeDateMonthIsMandatory {
            get {
                return ResourceManager.GetString("BestBeforeDateMonthIsMandatory", resourceCulture);
            }
        }
        
        public static string NoResults {
            get {
                return ResourceManager.GetString("NoResults", resourceCulture);
            }
        }
        
        public static string EasiExport {
            get {
                return ResourceManager.GetString("EasiExport", resourceCulture);
            }
        }
        
        public static string EasiAdfinityInterface {
            get {
                return ResourceManager.GetString("EasiAdfinityInterface", resourceCulture);
            }
        }
        
        public static string APIParameters {
            get {
                return ResourceManager.GetString("APIParameters", resourceCulture);
            }
        }
        
        public static string WeighingSlip {
            get {
                return ResourceManager.GetString("WeighingSlip", resourceCulture);
            }
        }
        
        public static string Configuration {
            get {
                return ResourceManager.GetString("Configuration", resourceCulture);
            }
        }
        
        public static string Connection {
            get {
                return ResourceManager.GetString("Connection", resourceCulture);
            }
        }
        
        public static string Username {
            get {
                return ResourceManager.GetString("Username", resourceCulture);
            }
        }
        
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        public static string BaseURL {
            get {
                return ResourceManager.GetString("BaseURL", resourceCulture);
            }
        }
        
        public static string AdfinityDatabase {
            get {
                return ResourceManager.GetString("AdfinityDatabase", resourceCulture);
            }
        }
        
        public static string AdfinityEnvir {
            get {
                return ResourceManager.GetString("AdfinityEnvir", resourceCulture);
            }
        }
        
        public static string ArticleReferenceSize {
            get {
                return ResourceManager.GetString("ArticleReferenceSize", resourceCulture);
            }
        }
        
        public static string Assignments {
            get {
                return ResourceManager.GetString("Assignments", resourceCulture);
            }
        }
        
        public static string SaveConfiguration {
            get {
                return ResourceManager.GetString("SaveConfiguration", resourceCulture);
            }
        }
        
        public static string FromWeighingSlipNumber {
            get {
                return ResourceManager.GetString("FromWeighingSlipNumber", resourceCulture);
            }
        }
        
        public static string ToWeighingSlipNumber {
            get {
                return ResourceManager.GetString("ToWeighingSlipNumber", resourceCulture);
            }
        }
        
        public static string ReExportExportedWeighingSlips {
            get {
                return ResourceManager.GetString("ReExportExportedWeighingSlips", resourceCulture);
            }
        }
        
        public static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        public static string CreateJob {
            get {
                return ResourceManager.GetString("CreateJob", resourceCulture);
            }
        }
        
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        public static string NoWeighingSlipsFound {
            get {
                return ResourceManager.GetString("NoWeighingSlipsFound", resourceCulture);
            }
        }
        
        public static string CountOfWeighingSlips {
            get {
                return ResourceManager.GetString("CountOfWeighingSlips", resourceCulture);
            }
        }
        
        public static string SuccessfullySent {
            get {
                return ResourceManager.GetString("SuccessfullySent", resourceCulture);
            }
        }
        
        public static string Duration {
            get {
                return ResourceManager.GetString("Duration", resourceCulture);
            }
        }
        
        public static string ReExportExportedInvoices {
            get {
                return ResourceManager.GetString("ReExportExportedInvoices", resourceCulture);
            }
        }
        
        public static string CountOfInvoices {
            get {
                return ResourceManager.GetString("CountOfInvoices", resourceCulture);
            }
        }
        
        public static string ServerName {
            get {
                return ResourceManager.GetString("ServerName", resourceCulture);
            }
        }
        
        public static string NameIsMandatory {
            get {
                return ResourceManager.GetString("NameIsMandatory", resourceCulture);
            }
        }
        
        public static string TimeInterval {
            get {
                return ResourceManager.GetString("TimeInterval", resourceCulture);
            }
        }
        
        public static string TimeIntervalIsMandatory {
            get {
                return ResourceManager.GetString("TimeIntervalIsMandatory", resourceCulture);
            }
        }
        
        public static string Create {
            get {
                return ResourceManager.GetString("Create", resourceCulture);
            }
        }
        
        public static string Tasks {
            get {
                return ResourceManager.GetString("Tasks", resourceCulture);
            }
        }
        
        public static string Jobs {
            get {
                return ResourceManager.GetString("Jobs", resourceCulture);
            }
        }
        
        public static string Reload {
            get {
                return ResourceManager.GetString("Reload", resourceCulture);
            }
        }
        
        public static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        public static string Data {
            get {
                return ResourceManager.GetString("Data", resourceCulture);
            }
        }
        
        public static string Server {
            get {
                return ResourceManager.GetString("Server", resourceCulture);
            }
        }
        
        public static string StartTime {
            get {
                return ResourceManager.GetString("StartTime", resourceCulture);
            }
        }
        
        public static string NextTriggerTime {
            get {
                return ResourceManager.GetString("NextTriggerTime", resourceCulture);
            }
        }
        
        public static string Execute {
            get {
                return ResourceManager.GetString("Execute", resourceCulture);
            }
        }
        
        public static string Stop {
            get {
                return ResourceManager.GetString("Stop", resourceCulture);
            }
        }
        
        public static string Start {
            get {
                return ResourceManager.GetString("Start", resourceCulture);
            }
        }
        
        public static string Logging {
            get {
                return ResourceManager.GetString("Logging", resourceCulture);
            }
        }
        
        public static string CreateTask {
            get {
                return ResourceManager.GetString("CreateTask", resourceCulture);
            }
        }
        
        public static string Normal {
            get {
                return ResourceManager.GetString("Normal", resourceCulture);
            }
        }
        
        public static string Paused {
            get {
                return ResourceManager.GetString("Paused", resourceCulture);
            }
        }
        
        public static string Completed {
            get {
                return ResourceManager.GetString("Completed", resourceCulture);
            }
        }
        
        public static string Blocked {
            get {
                return ResourceManager.GetString("Blocked", resourceCulture);
            }
        }
        
        public static string NotAvailable {
            get {
                return ResourceManager.GetString("NotAvailable", resourceCulture);
            }
        }
        
        public static string Unknown {
            get {
                return ResourceManager.GetString("Unknown", resourceCulture);
            }
        }
        
        public static string Logs {
            get {
                return ResourceManager.GetString("Logs", resourceCulture);
            }
        }
        
        public static string ComboBoxes {
            get {
                return ResourceManager.GetString("ComboBoxes", resourceCulture);
            }
        }
        
        public static string ComboBoxesConfiguration {
            get {
                return ResourceManager.GetString("ComboBoxesConfiguration", resourceCulture);
            }
        }
        
        public static string Index {
            get {
                return ResourceManager.GetString("Index", resourceCulture);
            }
        }
        
        public static string CreateComboBox {
            get {
                return ResourceManager.GetString("CreateComboBox", resourceCulture);
            }
        }
        
        public static string EditComboBox {
            get {
                return ResourceManager.GetString("EditComboBox", resourceCulture);
            }
        }
        
        public static string ElectronicInvoice {
            get {
                return ResourceManager.GetString("ElectronicInvoice", resourceCulture);
            }
        }
        
        public static string ElectronicInvoiceConfiguration {
            get {
                return ResourceManager.GetString("ElectronicInvoiceConfiguration", resourceCulture);
            }
        }
        
        public static string SenderName {
            get {
                return ResourceManager.GetString("SenderName", resourceCulture);
            }
        }
        
        public static string SenderEmail {
            get {
                return ResourceManager.GetString("SenderEmail", resourceCulture);
            }
        }
        
        public static string Port {
            get {
                return ResourceManager.GetString("Port", resourceCulture);
            }
        }
        
        public static string TimeoutInMilliseconds {
            get {
                return ResourceManager.GetString("TimeoutInMilliseconds", resourceCulture);
            }
        }
        
        public static string PrintLayout {
            get {
                return ResourceManager.GetString("PrintLayout", resourceCulture);
            }
        }
        
        public static string OpenDesignerForEInvoice {
            get {
                return ResourceManager.GetString("OpenDesignerForEInvoice", resourceCulture);
            }
        }
        
        public static string SendTestEmail {
            get {
                return ResourceManager.GetString("SendTestEmail", resourceCulture);
            }
        }
        
        public static string DeleteEmailConfiguration {
            get {
                return ResourceManager.GetString("DeleteEmailConfiguration", resourceCulture);
            }
        }
        
        public static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        public static string FileName {
            get {
                return ResourceManager.GetString("FileName", resourceCulture);
            }
        }
        
        public static string SelectCountry {
            get {
                return ResourceManager.GetString("SelectCountry", resourceCulture);
            }
        }
        
        public static string EmailCouldNotBeCreated {
            get {
                return ResourceManager.GetString("EmailCouldNotBeCreated", resourceCulture);
            }
        }
        
        public static string EmailTestPreview {
            get {
                return ResourceManager.GetString("EmailTestPreview", resourceCulture);
            }
        }
        
        public static string To {
            get {
                return ResourceManager.GetString("To", resourceCulture);
            }
        }
        
        public static string Attachment {
            get {
                return ResourceManager.GetString("Attachment", resourceCulture);
            }
        }
        
        public static string APIKeys {
            get {
                return ResourceManager.GetString("APIKeys", resourceCulture);
            }
        }
        
        public static string Key {
            get {
                return ResourceManager.GetString("Key", resourceCulture);
            }
        }
        
        public static string AddAPIKey {
            get {
                return ResourceManager.GetString("AddAPIKey", resourceCulture);
            }
        }
        
        public static string License {
            get {
                return ResourceManager.GetString("License", resourceCulture);
            }
        }
        
        public static string AddFromClipboard {
            get {
                return ResourceManager.GetString("AddFromClipboard", resourceCulture);
            }
        }
        
        public static string LicenseData {
            get {
                return ResourceManager.GetString("LicenseData", resourceCulture);
            }
        }
        
        public static string LicenseInformation {
            get {
                return ResourceManager.GetString("LicenseInformation", resourceCulture);
            }
        }
        
        public static string ValidUntil {
            get {
                return ResourceManager.GetString("ValidUntil", resourceCulture);
            }
        }
        
        public static string Modules {
            get {
                return ResourceManager.GetString("Modules", resourceCulture);
            }
        }
        
        public static string Licenses {
            get {
                return ResourceManager.GetString("Licenses", resourceCulture);
            }
        }
        
        public static string InvalidLicenseDataError {
            get {
                return ResourceManager.GetString("InvalidLicenseDataError", resourceCulture);
            }
        }
        
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        public static string ZUGFeRD {
            get {
                return ResourceManager.GetString("ZUGFeRD", resourceCulture);
            }
        }
        
        public static string ZUGFeRDXRechnung {
            get {
                return ResourceManager.GetString("ZUGFeRDXRechnung", resourceCulture);
            }
        }
        
        public static string XRechnung {
            get {
                return ResourceManager.GetString("XRechnung", resourceCulture);
            }
        }
        
        public static string EDIFACTTrueCommerce {
            get {
                return ResourceManager.GetString("EDIFACTTrueCommerce", resourceCulture);
            }
        }
        
        public static string EDIFACTStradEdi {
            get {
                return ResourceManager.GetString("EDIFACTStradEdi", resourceCulture);
            }
        }
        
        public static string DatabaseIsBeingLoaded {
            get {
                return ResourceManager.GetString("DatabaseIsBeingLoaded", resourceCulture);
            }
        }
        
        public static string ProcessArticleSearch {
            get {
                return ResourceManager.GetString("ProcessArticleSearch", resourceCulture);
            }
        }
        
        public static string NoOrderFound {
            get {
                return ResourceManager.GetString("NoOrderFound", resourceCulture);
            }
        }
        
        public static string Orders {
            get {
                return ResourceManager.GetString("Orders", resourceCulture);
            }
        }
        
        public static string WithCustomer {
            get {
                return ResourceManager.GetString("WithCustomer", resourceCulture);
            }
        }
        
        public static string WithoutCustomer {
            get {
                return ResourceManager.GetString("WithoutCustomer", resourceCulture);
            }
        }
        
        public static string OnlyCustomer {
            get {
                return ResourceManager.GetString("OnlyCustomer", resourceCulture);
            }
        }
        
        public static string CommissionTrack {
            get {
                return ResourceManager.GetString("CommissionTrack", resourceCulture);
            }
        }
        
        public static string Order {
            get {
                return ResourceManager.GetString("Order", resourceCulture);
            }
        }
        
        public static string PrintLayoutForDeliveryNote {
            get {
                return ResourceManager.GetString("PrintLayoutForDeliveryNote", resourceCulture);
            }
        }
        
        public static string BeginningExportDate {
            get {
                return ResourceManager.GetString("BeginningExportDate", resourceCulture);
            }
        }
        
        public static string PrintLayoutForPalletNote {
            get {
                return ResourceManager.GetString("PrintLayoutForPalletNote", resourceCulture);
            }
        }
        
        public static string DATEVExport {
            get {
                return ResourceManager.GetString("DATEVExport", resourceCulture);
            }
        }
        
        public static string ExportParameters {
            get {
                return ResourceManager.GetString("ExportParameters", resourceCulture);
            }
        }
        
        public static string StartOfFinancialYear {
            get {
                return ResourceManager.GetString("StartOfFinancialYear", resourceCulture);
            }
        }
        
        public static string ConsultantNumber {
            get {
                return ResourceManager.GetString("ConsultantNumber", resourceCulture);
            }
        }
        
        public static string GeneralLedgerAccountLength {
            get {
                return ResourceManager.GetString("GeneralLedgerAccountLength", resourceCulture);
            }
        }
        
        public static string SuffixForAccounts {
            get {
                return ResourceManager.GetString("SuffixForAccounts", resourceCulture);
            }
        }
        
        public static string FilePath {
            get {
                return ResourceManager.GetString("FilePath", resourceCulture);
            }
        }
        
        public static string UseExternalInvoiceNumberForIncomingInvoice {
            get {
                return ResourceManager.GetString("UseExternalInvoiceNumberForIncomingInvoice", resourceCulture);
            }
        }
        
        public static string ExportOfInvoicesSuccessfullyCompleted {
            get {
                return ResourceManager.GetString("ExportOfInvoicesSuccessfullyCompleted", resourceCulture);
            }
        }
        
        public static string GeneralLedgerAccountLengthError {
            get {
                return ResourceManager.GetString("GeneralLedgerAccountLengthError", resourceCulture);
            }
        }
        
        public static string StartOfFinancialYearFirstDayOfMonthError {
            get {
                return ResourceManager.GetString("StartOfFinancialYearFirstDayOfMonthError", resourceCulture);
            }
        }
        
        public static string EDIFACTExport {
            get {
                return ResourceManager.GetString("EDIFACTExport", resourceCulture);
            }
        }
        
        public static string ADDISONAKTEtsenitExport {
            get {
                return ResourceManager.GetString("ADDISONAKTEtsenitExport", resourceCulture);
            }
        }
        
        public static string FromCreationDate {
            get {
                return ResourceManager.GetString("FromCreationDate", resourceCulture);
            }
        }
        
        public static string FromInvoiceDate {
            get {
                return ResourceManager.GetString("FromInvoiceDate", resourceCulture);
            }
        }
        
        public static string UntilCreationDate {
            get {
                return ResourceManager.GetString("UntilCreationDate", resourceCulture);
            }
        }
        
        public static string UntilInvoiceDate {
            get {
                return ResourceManager.GetString("UntilInvoiceDate", resourceCulture);
            }
        }
        
        public static string UntilInvoiceNumber {
            get {
                return ResourceManager.GetString("UntilInvoiceNumber", resourceCulture);
            }
        }
        
        public static string ADDISONClientNumber {
            get {
                return ResourceManager.GetString("ADDISONClientNumber", resourceCulture);
            }
        }
        
        public static string UseInvoiceDateInsteadOfSystemDateForExport {
            get {
                return ResourceManager.GetString("UseInvoiceDateInsteadOfSystemDateForExport", resourceCulture);
            }
        }
        
        public static string WithSubsequentBooking {
            get {
                return ResourceManager.GetString("WithSubsequentBooking", resourceCulture);
            }
        }
        
        public static string SubsequentPostingsForVATAreGeneratedAutomatically {
            get {
                return ResourceManager.GetString("SubsequentPostingsForVATAreGeneratedAutomatically", resourceCulture);
            }
        }
        
        public static string ADDISONPipelineExport {
            get {
                return ResourceManager.GetString("ADDISONPipelineExport", resourceCulture);
            }
        }
        
        public static string FileNameWithTimestamp {
            get {
                return ResourceManager.GetString("FileNameWithTimestamp", resourceCulture);
            }
        }
        
        public static string EDIFACT {
            get {
                return ResourceManager.GetString("EDIFACT", resourceCulture);
            }
        }
        
        public static string DATEV {
            get {
                return ResourceManager.GetString("DATEV", resourceCulture);
            }
        }
        
        public static string ADDISONAKTEtsenit {
            get {
                return ResourceManager.GetString("ADDISONAKTEtsenit", resourceCulture);
            }
        }
        
        public static string ADDISONPipeline {
            get {
                return ResourceManager.GetString("ADDISONPipeline", resourceCulture);
            }
        }
        
        public static string EasiAdfinity {
            get {
                return ResourceManager.GetString("EasiAdfinity", resourceCulture);
            }
        }
        
        public static string SearchForSuppliers {
            get {
                return ResourceManager.GetString("SearchForSuppliers", resourceCulture);
            }
        }
        
        public static string SearchAccounting {
            get {
                return ResourceManager.GetString("SearchAccounting", resourceCulture);
            }
        }
        
        public static string LoadWeighingSlips {
            get {
                return ResourceManager.GetString("LoadWeighingSlips", resourceCulture);
            }
        }
        
        public static string ContinueAccounting {
            get {
                return ResourceManager.GetString("ContinueAccounting", resourceCulture);
            }
        }
        
        public static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        public static string NoSupplierFound {
            get {
                return ResourceManager.GetString("NoSupplierFound", resourceCulture);
            }
        }
        
        public static string NoSupplierFoundForSearchTerm {
            get {
                return ResourceManager.GetString("NoSupplierFoundForSearchTerm", resourceCulture);
            }
        }
        
        public static string PleaseEnterAValidAccountingNumber {
            get {
                return ResourceManager.GetString("PleaseEnterAValidAccountingNumber", resourceCulture);
            }
        }
        
        public static string EnterAccountingNumber {
            get {
                return ResourceManager.GetString("EnterAccountingNumber", resourceCulture);
            }
        }
        
        public static string WeighingSlipNumber {
            get {
                return ResourceManager.GetString("WeighingSlipNumber", resourceCulture);
            }
        }
        
        public static string SupplierNumber {
            get {
                return ResourceManager.GetString("SupplierNumber", resourceCulture);
            }
        }
        
        public static string Item {
            get {
                return ResourceManager.GetString("Item", resourceCulture);
            }
        }
        
        public static string Weight {
            get {
                return ResourceManager.GetString("Weight", resourceCulture);
            }
        }
        
        public static string Apply {
            get {
                return ResourceManager.GetString("Apply", resourceCulture);
            }
        }
        
        public static string InconsistentSelection {
            get {
                return ResourceManager.GetString("InconsistentSelection", resourceCulture);
            }
        }
        
        public static string AllSelectedItemsSameWeighingSlipSupplierNumber {
            get {
                return ResourceManager.GetString("AllSelectedItemsSameWeighingSlipSupplierNumber", resourceCulture);
            }
        }
        
        public static string SelectionError {
            get {
                return ResourceManager.GetString("SelectionError", resourceCulture);
            }
        }
        
        public static string OnlySelectItemsWithSameWeighingSlipSupplierNumber {
            get {
                return ResourceManager.GetString("OnlySelectItemsWithSameWeighingSlipSupplierNumber", resourceCulture);
            }
        }
        
        public static string NoSelection {
            get {
                return ResourceManager.GetString("NoSelection", resourceCulture);
            }
        }
        
        public static string SelectAtLeastOneWeighingSlip {
            get {
                return ResourceManager.GetString("SelectAtLeastOneWeighingSlip", resourceCulture);
            }
        }
        
        public static string WeighingSlipsUsedForBillingConfirmation {
            get {
                return ResourceManager.GetString("WeighingSlipsUsedForBillingConfirmation", resourceCulture);
            }
        }
        
        public static string Confirmation {
            get {
                return ResourceManager.GetString("Confirmation", resourceCulture);
            }
        }
        
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        public static string AnErrorOccuredWhenCreatingTheInvoice {
            get {
                return ResourceManager.GetString("AnErrorOccuredWhenCreatingTheInvoice", resourceCulture);
            }
        }
        
        public static string AccountingInformationIsLoaded {
            get {
                return ResourceManager.GetString("AccountingInformationIsLoaded", resourceCulture);
            }
        }
        
        public static string ViewModeReadOnly {
            get {
                return ResourceManager.GetString("ViewModeReadOnly", resourceCulture);
            }
        }
        
        public static string GrainAccountingAlreadyEstablished {
            get {
                return ResourceManager.GetString("GrainAccountingAlreadyEstablished", resourceCulture);
            }
        }
        
        public static string YouMustDeleteTheEntryToChangeAnything {
            get {
                return ResourceManager.GetString("YouMustDeleteTheEntryToChangeAnything", resourceCulture);
            }
        }
        
        public static string EditModeActive {
            get {
                return ResourceManager.GetString("EditModeActive", resourceCulture);
            }
        }
        
        public static string ShowLess {
            get {
                return ResourceManager.GetString("ShowLess", resourceCulture);
            }
        }
        
        public static string ShowMore {
            get {
                return ResourceManager.GetString("ShowMore", resourceCulture);
            }
        }
        
        public static string Supplier {
            get {
                return ResourceManager.GetString("Supplier", resourceCulture);
            }
        }
        
        public static string InvoiceOffice {
            get {
                return ResourceManager.GetString("InvoiceOffice", resourceCulture);
            }
        }
        
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        public static string DeleteWeighingSlip {
            get {
                return ResourceManager.GetString("DeleteWeighingSlip", resourceCulture);
            }
        }
        
        public static string Contract {
            get {
                return ResourceManager.GetString("Contract", resourceCulture);
            }
        }
        
        public static string NoContract {
            get {
                return ResourceManager.GetString("NoContract", resourceCulture);
            }
        }
        
        public static string Designation {
            get {
                return ResourceManager.GetString("Designation", resourceCulture);
            }
        }
        
        public static string LaboratoryValue {
            get {
                return ResourceManager.GetString("LaboratoryValue", resourceCulture);
            }
        }
        
        public static string UnitPrice {
            get {
                return ResourceManager.GetString("UnitPrice", resourceCulture);
            }
        }
        
        public static string TotalPrice {
            get {
                return ResourceManager.GetString("TotalPrice", resourceCulture);
            }
        }
        
        public static string ResetValue {
            get {
                return ResourceManager.GetString("ResetValue", resourceCulture);
            }
        }
        
        public static string EditValue {
            get {
                return ResourceManager.GetString("EditValue", resourceCulture);
            }
        }
        
        public static string DeleteValue {
            get {
                return ResourceManager.GetString("DeleteValue", resourceCulture);
            }
        }
        
        public static string AddValue {
            get {
                return ResourceManager.GetString("AddValue", resourceCulture);
            }
        }
        
        public static string NetWeight {
            get {
                return ResourceManager.GetString("NetWeight", resourceCulture);
            }
        }
        
        public static string NetPrice {
            get {
                return ResourceManager.GetString("NetPrice", resourceCulture);
            }
        }
        
        public static string AddWeighingSlips {
            get {
                return ResourceManager.GetString("AddWeighingSlips", resourceCulture);
            }
        }
        
        public static string DeliveryWeight {
            get {
                return ResourceManager.GetString("DeliveryWeight", resourceCulture);
            }
        }
        
        public static string Deductions {
            get {
                return ResourceManager.GetString("Deductions", resourceCulture);
            }
        }
        
        public static string AccountingWeight {
            get {
                return ResourceManager.GetString("AccountingWeight", resourceCulture);
            }
        }
        
        public static string CounterfoilNo {
            get {
                return ResourceManager.GetString("CounterfoilNo", resourceCulture);
            }
        }
        
        public static string AccountingDate {
            get {
                return ResourceManager.GetString("AccountingDate", resourceCulture);
            }
        }
        
        public static string GrossAmount {
            get {
                return ResourceManager.GetString("GrossAmount", resourceCulture);
            }
        }
        
        public static string PrintPreview {
            get {
                return ResourceManager.GetString("PrintPreview", resourceCulture);
            }
        }
        
        public static string Complete {
            get {
                return ResourceManager.GetString("Complete", resourceCulture);
            }
        }
        
        public static string ContractNo {
            get {
                return ResourceManager.GetString("ContractNo", resourceCulture);
            }
        }
        
        public static string AddContract {
            get {
                return ResourceManager.GetString("AddContract", resourceCulture);
            }
        }
        
        public static string NoContractsConnected {
            get {
                return ResourceManager.GetString("NoContractsConnected", resourceCulture);
            }
        }
        
        public static string ErrorDuringLoading {
            get {
                return ResourceManager.GetString("ErrorDuringLoading", resourceCulture);
            }
        }
        
        public static string ErrorRetrievingTheHeader {
            get {
                return ResourceManager.GetString("ErrorRetrievingTheHeader", resourceCulture);
            }
        }
        
        public static string NotFound {
            get {
                return ResourceManager.GetString("NotFound", resourceCulture);
            }
        }
        
        public static string GrainAccountingWithIDNotFound {
            get {
                return ResourceManager.GetString("GrainAccountingWithIDNotFound", resourceCulture);
            }
        }
        
        public static string ErrorWhenRetrievingThePositions {
            get {
                return ResourceManager.GetString("ErrorWhenRetrievingThePositions", resourceCulture);
            }
        }
        
        public static string SupplierWithNumberNotFound {
            get {
                return ResourceManager.GetString("SupplierWithNumberNotFound", resourceCulture);
            }
        }
        
        public static string ErrorRetrievingTheSupplier {
            get {
                return ResourceManager.GetString("ErrorRetrievingTheSupplier", resourceCulture);
            }
        }
        
        public static string DataError {
            get {
                return ResourceManager.GetString("DataError", resourceCulture);
            }
        }
        
        public static string NoSupplierNumberFoundInHeader {
            get {
                return ResourceManager.GetString("NoSupplierNumberFoundInHeader", resourceCulture);
            }
        }
        
        public static string ErrorWhenRetrievingTheContract {
            get {
                return ResourceManager.GetString("ErrorWhenRetrievingTheContract", resourceCulture);
            }
        }
        
        public static string Action {
            get {
                return ResourceManager.GetString("Action", resourceCulture);
            }
        }
        
        public static string EditingForWeighingSlipInvoked {
            get {
                return ResourceManager.GetString("EditingForWeighingSlipInvoked", resourceCulture);
            }
        }
        
        public static string EditingCanceled {
            get {
                return ResourceManager.GetString("EditingCanceled", resourceCulture);
            }
        }
        
        public static string ChangesForWeighingSlipSaved {
            get {
                return ResourceManager.GetString("ChangesForWeighingSlipSaved", resourceCulture);
            }
        }
        
        public static string AreYouSure {
            get {
                return ResourceManager.GetString("AreYouSure", resourceCulture);
            }
        }
        
        public static string ReallyDeleteWeighingSlip {
            get {
                return ResourceManager.GetString("ReallyDeleteWeighingSlip", resourceCulture);
            }
        }
        
        public static string Success {
            get {
                return ResourceManager.GetString("Success", resourceCulture);
            }
        }
        
        public static string WeighingSlipRemoved {
            get {
                return ResourceManager.GetString("WeighingSlipRemoved", resourceCulture);
            }
        }
        
        public static string ResetForCalled {
            get {
                return ResourceManager.GetString("ResetForCalled", resourceCulture);
            }
        }
        
        public static string ValueUpdated {
            get {
                return ResourceManager.GetString("ValueUpdated", resourceCulture);
            }
        }
        
        public static string UpdateFailed {
            get {
                return ResourceManager.GetString("UpdateFailed", resourceCulture);
            }
        }
        
        public static string ErrorDuringEditing {
            get {
                return ResourceManager.GetString("ErrorDuringEditing", resourceCulture);
            }
        }
        
        public static string Confirm {
            get {
                return ResourceManager.GetString("Confirm", resourceCulture);
            }
        }
        
        public static string ReallyDeleteValue {
            get {
                return ResourceManager.GetString("ReallyDeleteValue", resourceCulture);
            }
        }
        
        public static string ValueRemoved {
            get {
                return ResourceManager.GetString("ValueRemoved", resourceCulture);
            }
        }
        
        public static string Info {
            get {
                return ResourceManager.GetString("Info", resourceCulture);
            }
        }
        
        public static string NewValueAddedPlaceholder {
            get {
                return ResourceManager.GetString("NewValueAddedPlaceholder", resourceCulture);
            }
        }
        
        public static string NewWeighingSlipAddedPlaceholder {
            get {
                return ResourceManager.GetString("NewWeighingSlipAddedPlaceholder", resourceCulture);
            }
        }
        
        public static string EnterNo {
            get {
                return ResourceManager.GetString("EnterNo", resourceCulture);
            }
        }
        
        public static string NewValue {
            get {
                return ResourceManager.GetString("NewValue", resourceCulture);
            }
        }
        
        public static string Unit {
            get {
                return ResourceManager.GetString("Unit", resourceCulture);
            }
        }
        
        public static string TotalPriceNet {
            get {
                return ResourceManager.GetString("TotalPriceNet", resourceCulture);
            }
        }
        
        public static string Eg {
            get {
                return ResourceManager.GetString("Eg", resourceCulture);
            }
        }
        
        public static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        public static string NoEntriesFound {
            get {
                return ResourceManager.GetString("NoEntriesFound", resourceCulture);
            }
        }
        
        public static string MaskNo {
            get {
                return ResourceManager.GetString("MaskNo", resourceCulture);
            }
        }
        
        public static string ParameterType {
            get {
                return ResourceManager.GetString("ParameterType", resourceCulture);
            }
        }
        
        public static string CalculationType {
            get {
                return ResourceManager.GetString("CalculationType", resourceCulture);
            }
        }
        
        public static string CalculateFrom {
            get {
                return ResourceManager.GetString("CalculateFrom", resourceCulture);
            }
        }
        
        public static string CalculateUntil {
            get {
                return ResourceManager.GetString("CalculateUntil", resourceCulture);
            }
        }
        
        public static string BaseValue {
            get {
                return ResourceManager.GetString("BaseValue", resourceCulture);
            }
        }
        
        public static string StepValue {
            get {
                return ResourceManager.GetString("StepValue", resourceCulture);
            }
        }
        
        public static string Factor {
            get {
                return ResourceManager.GetString("Factor", resourceCulture);
            }
        }
        
        public static string BaseValueFrom {
            get {
                return ResourceManager.GetString("BaseValueFrom", resourceCulture);
            }
        }
        
        public static string BaseValueTo {
            get {
                return ResourceManager.GetString("BaseValueTo", resourceCulture);
            }
        }
        
        public static string ModifierValueStart {
            get {
                return ResourceManager.GetString("ModifierValueStart", resourceCulture);
            }
        }
        
        public static string ModifierValueStep {
            get {
                return ResourceManager.GetString("ModifierValueStep", resourceCulture);
            }
        }
        
        public static string SavingFailed {
            get {
                return ResourceManager.GetString("SavingFailed", resourceCulture);
            }
        }
        
        public static string AllFieldsMustBeFilled {
            get {
                return ResourceManager.GetString("AllFieldsMustBeFilled", resourceCulture);
            }
        }
        
        public static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        public static string NoAccountingFound {
            get {
                return ResourceManager.GetString("NoAccountingFound", resourceCulture);
            }
        }
        
        public static string Open {
            get {
                return ResourceManager.GetString("Open", resourceCulture);
            }
        }
        
        public static string Price {
            get {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }
        
        public static string ErrorDuringLoadingTheData {
            get {
                return ResourceManager.GetString("ErrorDuringLoadingTheData", resourceCulture);
            }
        }
        
        public static string GrainParameters {
            get {
                return ResourceManager.GetString("GrainParameters", resourceCulture);
            }
        }
        
        public static string MaskType {
            get {
                return ResourceManager.GetString("MaskType", resourceCulture);
            }
        }
        
        public static string Humidity {
            get {
                return ResourceManager.GetString("Humidity", resourceCulture);
            }
        }
        
        public static string Money {
            get {
                return ResourceManager.GetString("Money", resourceCulture);
            }
        }
        
        public static string Trimming {
            get {
                return ResourceManager.GetString("Trimming", resourceCulture);
            }
        }
        
        public static string Import {
            get {
                return ResourceManager.GetString("Import", resourceCulture);
            }
        }
        
        public static string OpenAPI {
            get {
                return ResourceManager.GetString("OpenAPI", resourceCulture);
            }
        }
        
        public static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        public static string NoOrderImported {
            get {
                return ResourceManager.GetString("NoOrderImported", resourceCulture);
            }
        }
        
        public static string OrderNumber {
            get {
                return ResourceManager.GetString("OrderNumber", resourceCulture);
            }
        }
        
        public static string PurchaseOrderNumber {
            get {
                return ResourceManager.GetString("PurchaseOrderNumber", resourceCulture);
            }
        }
        
        public static string OrderDate {
            get {
                return ResourceManager.GetString("OrderDate", resourceCulture);
            }
        }
        
        public static string DeliveryFromDate {
            get {
                return ResourceManager.GetString("DeliveryFromDate", resourceCulture);
            }
        }
        
        public static string DeliveryToDate {
            get {
                return ResourceManager.GetString("DeliveryToDate", resourceCulture);
            }
        }
        
        public static string ImportOfOrdersWasSuccessfullyCompleted {
            get {
                return ResourceManager.GetString("ImportOfOrdersWasSuccessfullyCompleted", resourceCulture);
            }
        }
        
        public static string Commission {
            get {
                return ResourceManager.GetString("Commission", resourceCulture);
            }
        }
        
        public static string Printer {
            get {
                return ResourceManager.GetString("Printer", resourceCulture);
            }
        }
        
        public static string WithRobot {
            get {
                return ResourceManager.GetString("WithRobot", resourceCulture);
            }
        }
        
        public static string SelectCommissionTrack {
            get {
                return ResourceManager.GetString("SelectCommissionTrack", resourceCulture);
            }
        }
        
        public static string PrintSettings {
            get {
                return ResourceManager.GetString("PrintSettings", resourceCulture);
            }
        }
        
        public static string GoodsRecipient {
            get {
                return ResourceManager.GetString("GoodsRecipient", resourceCulture);
            }
        }
        
        public static string PalletCount {
            get {
                return ResourceManager.GetString("PalletCount", resourceCulture);
            }
        }
        
        public static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        public static string CommissionButton {
            get {
                return ResourceManager.GetString("CommissionButton", resourceCulture);
            }
        }
        
        public static string PalletTypeIsMandatory {
            get {
                return ResourceManager.GetString("PalletTypeIsMandatory", resourceCulture);
            }
        }
    }
}
