namespace WingCore.domain.Common.Configurations;

public record ConfigurationElectricWgs : ConfigurationBase
{
    public Dictionary<string, LanguageAndEmailFields> LanguageToEmailFields { get; set; } = [];
    public string SmtpSenderAddress { get; set; } = string.Empty;
    public string SmtpSenderName { get; set; } = string.Empty;
    
    public string SmtpServerAddress { get; set; } = string.Empty;
    public int SmtpServerPort { get; set; }
    public string SmtpServerUser { get; set; } = string.Empty;
    public string SmtpServerPassword { get; set; } = string.Empty;
    public int SmtpSocketTimeout { get; set; }
    public bool SmtpSecureConnection { get; set; }
    
    public bool ShowDialog { get; set; }
    public bool ForceSendMailInSeparateThread { get; set; }
    
    public LanguageAndEmailFields GetEmailConfig(string languageToUse, string fallBackLanguage)
    {
        LanguageAndEmailFields? emailBodyParameter = null;
        var usedCountry = new List<string>();
        if (!string.IsNullOrWhiteSpace(languageToUse))
        {
            usedCountry.Add(languageToUse);
            LanguageToEmailFields.TryGetValue(languageToUse, out emailBodyParameter);
        }
        
        if (emailBodyParameter is null && !string.IsNullOrWhiteSpace(fallBackLanguage))
        {
            usedCountry.Add(fallBackLanguage);
            LanguageToEmailFields.TryGetValue(fallBackLanguage, out emailBodyParameter);
        }

        if (emailBodyParameter is null && usedCountry.Count > 1)
            throw new Exception($"Es existiert keine E-Mail konfigurationen für die Länder '{string.Join(",", usedCountry)}'");

        if (emailBodyParameter is null)
            throw new Exception($"Es existiert keine E-Mail konfiguration für das Land '{string.Join(",", usedCountry)}'");
        
        return emailBodyParameter;
    }
}
