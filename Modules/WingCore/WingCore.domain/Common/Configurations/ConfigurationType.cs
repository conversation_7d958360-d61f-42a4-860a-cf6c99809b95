namespace WingCore.domain.Common.Configurations;

public record ConfigurationType(string Value)
{
    public static readonly ConfigurationType Empty = new("");
    public static readonly ConfigurationType DatevExportConfiguration = new(DatevExportConfigurationString);
    public static readonly ConfigurationType EdifactExportConfiguration = new(EdifactExportConfigurationString);
    public static readonly ConfigurationType AddisonTseNitExportConfiguration = new(AddisonTseNitExportConfigurationString);
    public static readonly ConfigurationType AddisonPipelineExportConfiguration = new(AddisonPipelineExportConfigurationString);
    public static readonly ConfigurationType ProgramLicConfiguration = new(ProgramLicConfigurationString);
    public static readonly ConfigurationType ElectricInvoiceConfiguration = new(ElectricInvoiceConfigurationString);
    public static readonly ConfigurationType LindeRoboterManagerConfiguration = new(LindeRoboterManagerConfigurationString);
    public static readonly ConfigurationType EasiAdfinityRestApiConfiguration = new(EasiAdfinityRestApiConfigurationString);
    public static readonly ConfigurationType CommissionConfiguration = new(CommissionConfigurationString);
    
    private const string DatevExportConfigurationString = "DATEV";
    private const string EdifactExportConfigurationString = "EDIFACT";
    private const string AddisonTseNitExportConfigurationString = "ADDISONTSENIT";
    private const string AddisonPipelineExportConfigurationString = "ADDISONPIPELINE";
    private const string ProgramLicConfigurationString = "PROGRAMLIC";
    private const string ElectricInvoiceConfigurationString = "ELECTRICINVOICE";
    private const string LindeRoboterManagerConfigurationString = "LINDEROBOTERMANAGER";
    private const string EasiAdfinityRestApiConfigurationString = "EASIADFINITYRESTAPI";
    private const string CommissionConfigurationString = "COMMISSION";
    
    public bool IsDatev() => Value == DatevExportConfigurationString;
    public bool IsProgramLic() => Value == ProgramLicConfigurationString;
}