using WingCore.domain.Common.Configurations;

namespace WingCore.application.Contract.Services;

public interface IConfigurationService
{
    public ValueTask<ConfigurationDatev> GetConfigurationDatevAsync(bool trackChanges = false);
    public ValueTask<ConfigurationEdifact> GetConfigurationEdifactAsync(bool trackChanges = false);
    public ValueTask<ConfigurationAddisonTseNit> GetConfigurationAddisonTseNitAsync(bool trackChanges = false);
    public ValueTask<ConfigurationAddisonPipeline> GetConfigurationAddisonPipelineAsync(bool trackChanges = false);
    public ValueTask<ConfigurationElectricInvoice> GetConfigurationElectricInvoiceAsync(bool trackChanges = false);
    public ValueTask<ConfigurationLindeRoboterManager> GetConfigurationLindeRoboterManagerAsync(bool trackChanges = false);
    public ValueTask<ConfigurationEasiAdfinityRestApi> GetConfigurationEasiAdfinityRestApiAsync(bool trackChanges = false);
    public ValueTask<ConfigurationCommission> GetConfigurationCommissionAsync(bool trackChanges = false);
    public ValueTask<long> SetConfigurationDatevAsync(ConfigurationDatev configuration);
    public ValueTask<long> SetConfigurationEdifactAsync(ConfigurationEdifact configuration);
    public ValueTask<long> SetConfigurationAddisonTseNitAsync(ConfigurationAddisonTseNit configuration);
    public ValueTask<long> SetConfigurationAddisonPipelineAsync(ConfigurationAddisonPipeline configuration);
    public ValueTask<long> SetConfigurationElectricInvoiceAsync(ConfigurationElectricInvoice configuration);
    public ValueTask<long> SetConfigurationLindeRoboterManagerAsync(ConfigurationLindeRoboterManager configuration);
    public ValueTask<long> SetConfigurationEasiAdfinityRestApiAsync(ConfigurationEasiAdfinityRestApi configuration);
    public ValueTask<long> SetConfigurationCommissionAsync(ConfigurationCommission configuration);
}