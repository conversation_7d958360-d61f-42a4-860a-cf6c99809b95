using MediatR;
using WingCore.application.ApiDtos;
using WingCore.application.ApiDtos.MasterData.V1;
using WingCore.application.Contract.IModels;

namespace WingCore.application.Api.Handler;

public class GetAllSupplierDtoV1QueryHandler(ILieferantenRepository lieferantenRepository) : IRequestHandler<GetAllSupplierDtoV1Query, ICollection<SupplierDtoV1>>
{
    public async Task<ICollection<SupplierDtoV1>> Handle(GetAllSupplierDtoV1Query request, CancellationToken cancellationToken)
    {
        var supplierList = await lieferantenRepository.GetSupplierOverLastChange(request.LastChange);
        return supplierList.Select(s => s.ToSupplierDtoV1()).ToList();
    }
}