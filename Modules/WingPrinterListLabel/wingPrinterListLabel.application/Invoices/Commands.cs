using MediatR;
using PrinterService.Models;
using WingCore.domain.Common.Configurations;

namespace wingPrinterListLabel.application.Invoices;


public sealed record DummyInvoiceSendEmailCommand(string Email,
                                                  string LanguageToUse,
                                                  ConfigurationElectricInvoice ConfigurationElectricInvoice) : IRequest<EmailDataToCreateEmail?>;
public sealed record DummyWgsSendEmailCommand(string Email,
                                              string LanguageToUse,
                                              ConfigurationElectricWgs ConfigurationElectricWgs) : IRequest<EmailDataToCreateEmail?>;
public sealed record OutgoingInvoicesSendEInvoiceEmailCommand(long InvoiceNumber, string LanguageToUse) : IRequest<EmailDataToCreateEmail?>;
public sealed record ZugferdOutgoingInvoiceSendEmailCommand(long InvoiceNumber, string LanguageToUse) : IRequest<EmailDataToCreateEmail?>;
public sealed record ZugferdOutgoingInvoiceAsPdfBase64Command(long InvoiceNumber, string LanguageToUse) : IRequest<string>;
