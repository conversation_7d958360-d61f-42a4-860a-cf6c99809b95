using MediatR;
using wingLager.application.Contracts;
using wingLager.application.Contracts.LindeQueueRepository;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.domain.LindeQueueModel;
using wingLager.domain.WarehouseManagementModels.Status;

namespace wingLager.application.LindeQueue.CreateQueueEntry;

public class CreateStoringLindeQueueEntryCommandHandler(ILindeQueueRepository lindeQueueRepositoryrepository, IWmStellplatzService stellplatzService, IWmStellplatzRepository stellplatzRepository, IPaletLagerRepository paletLagerRepository) : IRequestHandler<CreateStoringLindeQueueEntryCommand, LindeQueueEntry>
{
    public async Task<LindeQueueEntry> Handle(CreateStoringLindeQueueEntryCommand request, CancellationToken cancellationToken)
    {
        var (paletLager,startLocation, hallenPositionInfo) = request;
        
        var stellplatz = await stellplatzService.GetByHallposition(hallenPositionInfo);
        if(stellplatz is null) 
            throw new Exception("Destination Stellplatz not found");
        
        if (!stellplatz.FlagWorker.IsVbrFlagSet())
        {
            stellplatz.Ean = paletLager.ArtEAN ?? string.Empty;
            stellplatz.Nve = paletLager.NVENr ?? string.Empty;
            stellplatz.PalletDescription = paletLager.ArtBez1 ?? string.Empty;
            stellplatz.ReferenceNumber = string.Empty;
            stellplatz.LoadType = WmStellplatzLoadType.FinishedProductLoadType;
            stellplatz.Status = WmStellplatzStatus.OccupiedStellplatz;
            stellplatz.PalletLagerId = paletLager.Id;
            await stellplatzRepository.UpdateAsync(stellplatz, cancellationToken);
        }

        paletLager.ClearLinde();
        paletLager.FlagsWorker.SetLindeQueueCreatedFlag();
        await paletLagerRepository.UpdateWithoutTracking(paletLager, "", cancellationToken);
        var lindeQueueEntry = paletLager.ToLindeQueueEntry(startLocation,hallenPositionInfo.HallenPosition);
        await lindeQueueRepositoryrepository.AddAsync(lindeQueueEntry, cancellationToken);
        
        return lindeQueueEntry;
    }
}