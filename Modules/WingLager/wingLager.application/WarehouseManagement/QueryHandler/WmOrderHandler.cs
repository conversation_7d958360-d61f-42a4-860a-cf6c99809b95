using MediatR;
using WingCore.application.Contract.IModels;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Queries;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.WarehouseManagement.QueryHandler;

public class GetAllOrderInWarehouseQueryHandler(IWmOrderService wmOrderService, IAuftragskopfRepository auftragskopfRepository) : IRequestHandler<GetAllOrderInWarehouseQuery, ICollection<WmOrder>>
{
    public async Task<ICollection<WmOrder>> Handle(GetAllOrderInWarehouseQuery request, CancellationToken cancellationToken)
    {
        var orders = await wmOrderService.GetAllAsync();
        if (!orders.Select(o => o.OrderNumber).ToList().Any())
            return [];
        var auftragKopfList = await auftragskopfRepository.GetAuftragskopfByOrderNumberList(orders.Select(o => o.OrderNumber).ToList());
        if(!auftragKopfList.Any())
            return orders;
        var ordernumberAuftragskopfDic = auftragKopfList.Where(a => a.Auftragsnummer.HasValue).ToDictionary(d => d.Auftragsnummer!.Value);
        foreach (var order in orders)
        {
            if (ordernumberAuftragskopfDic.TryGetValue(order.OrderNumber, out var data))
            {
                order.Auftragskopf = data;
            }
        }
        return orders;
    }
}